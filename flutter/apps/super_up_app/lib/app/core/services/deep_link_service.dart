// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import '../../modules/peer_profile/views/peer_profile_view.dart';
import '../../modules/public_profile/views/public_profile_view.dart';
import '../../../main.dart';

class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;
  Uri? _pendingDeepLink;

  Future<void> initialize() async {
    _appLinks = AppLinks();

    // Handle app launch from deep link
    final initialUri = await _appLinks.getInitialLink();
    if (initialUri != null) {
      print('Initial deep link: $initialUri');
      // Store the deep link to handle after app is fully initialized
      _pendingDeepLink = initialUri;
      // Delay handling to ensure app is fully initialized
      Future.delayed(const Duration(milliseconds: 2000), () {
        _handleDeepLink(initialUri);
      });
    }

    // Handle deep links while app is running
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (uri) {
        print('Received deep link while app running: $uri');
        _handleDeepLink(uri);
      },
      onError: (err) {
        print('Deep link error: $err');
      },
    );

    print('Deep link service initialized');
  }

  void _handleDeepLink(Uri uri) {
    print('=== DEEP LINK RECEIVED ===');
    print('Full URI: $uri');
    print('Scheme: ${uri.scheme}');
    print('Host: ${uri.host}');
    print('Path: ${uri.path}');
    print('Path segments: ${uri.pathSegments}');

    // Check if it's a profile link
    if (uri.pathSegments.isNotEmpty) {
      final userId = uri.pathSegments[
          0]; // For orbit://profile/USER_ID, the user ID is the first segment
      print('Extracted user ID: $userId');
      _navigateToProfile(userId);
    } else {
      print('Deep link missing user ID - path segments: ${uri.pathSegments}');
    }
  }

  void _navigateToProfile(String userId) {
    print('Attempting to navigate to profile: $userId');

    // Get the current context from the navigator key
    final context = navigatorKey.currentContext;
    if (context == null) {
      print('No context available for navigation');
      return;
    }

    // Check if user is authenticated
    bool isAuthenticated = false;
    try {
      AppAuth.myProfile; // This will throw if not authenticated
      isAuthenticated = true;
      print('User is authenticated');
    } catch (e) {
      isAuthenticated = false;
      print('User is not authenticated');
    }

    print(
        'Navigating to ${isAuthenticated ? 'PeerProfileView' : 'PublicProfileView'}');

    // Navigate to appropriate profile page
    try {
      if (isAuthenticated) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PeerProfileView(peerId: userId),
          ),
        );
        print('Successfully navigated to PeerProfileView for user: $userId');
      } else {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PublicProfileView(userId: userId),
          ),
        );
        print('Successfully navigated to PublicProfileView for user: $userId');
      }
    } catch (e) {
      print('Navigation failed: $e');
    }
  }

  void _showDeepLinkNotification(String message) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  // Test method to manually trigger deep link handling
  void testDeepLink(String userId) {
    final testUri = Uri.parse('orbit://profile/$userId');
    _handleDeepLink(testUri);
  }

  void dispose() {
    _linkSubscription?.cancel();
  }
}
