// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import '../../modules/peer_profile/views/peer_profile_view.dart';
import '../../modules/public_profile/views/public_profile_view.dart';
import '../../../main.dart';

class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  Future<void> initialize() async {
    _appLinks = AppLinks();

    // Handle app launch from deep link
    final initialUri = await _appLinks.getInitialLink();
    if (initialUri != null) {
      print('Initial deep link: $initialUri');
      // Delay handling to ensure app is fully initialized
      Future.delayed(const Duration(milliseconds: 500), () {
        _handleDeepLink(initialUri);
      });
    }

    // Handle deep links while app is running
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (uri) {
        print('Received deep link while app running: $uri');
        _handleDeepLink(uri);
      },
      onError: (err) {
        print('Deep link error: $err');
      },
    );
  }

  void _handleDeepLink(Uri uri) {
    print('Handling deep link: $uri');

    // Check if it's a profile link from api.orbit.ke or orbit.ke
    if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'profile') {
      final userId = uri.pathSegments[1];
      _navigateToProfile(userId);
    }
  }

  void _navigateToProfile(String userId) {
    print('Attempting to navigate to profile: $userId');

    // Get the current context from the navigator key
    final context = navigatorKey.currentContext;
    if (context == null) {
      print('No context available for navigation');
      return;
    }

    // Check if user is authenticated
    bool isAuthenticated = false;
    try {
      AppAuth.myProfile; // This will throw if not authenticated
      isAuthenticated = true;
      print('User is authenticated');
    } catch (e) {
      isAuthenticated = false;
      print('User is not authenticated');
    }

    print(
        'Navigating to ${isAuthenticated ? 'PeerProfileView' : 'PublicProfileView'}');

    // Navigate to appropriate profile page
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => isAuthenticated
            ? PeerProfileView(peerId: userId)
            : PublicProfileView(userId: userId),
      ),
    );
  }

  void dispose() {
    _linkSubscription?.cancel();
  }
}
